from typing import List, Tuple
from config import load_config

class LanguageSettings:
    def __init__(self, languages: str = None):
        # Load language mappings from YAML configuration
        self._lang_config = load_config('language_mappings')
        self.languages = self._parse_language(languages)
        self.default_language = self.languages[0] if self.languages else self._lang_config.get('default_language',
                                                                                               'en-US')

    @property
    def language_codes_to_names(self) -> dict:
        """Get language codes to names mapping from configuration."""
        return self._lang_config.get('language_codes_to_names', {})

    @property
    def language_codes_to_locale(self) -> dict:
        """Get language codes to locale mapping from configuration."""
        return self._lang_config.get('language_codes_to_locale', {})



    def map_language_code(self, lang_code: str) -> Tuple[str, str]:
        lang_code = lang_code.lower()
        language_name = self.language_codes_to_names.get(lang_code, 'English')
        locale = self.language_codes_to_locale.get(lang_code, 'en-US')
        return language_name, locale

    def _parse_language(self, language_str: str) -> List[str]:
        """Parse language string into list of languages."""
        if not language_str:
            return [self._lang_config.get('default_language', 'en-US')]

        import re
        separator_pattern = self._lang_config.get('language_parsing.separators', '[;,]')
        languages = [lang.strip() for lang in re.split(separator_pattern, language_str) if lang.strip()]
        return languages

    def get_language_instructions(self) -> str:
        """Get language instructions based on configured languages."""
        messages_config = load_config('messages')

        if len(self.languages) == 1:
            lang_name, _ = self.map_language_code(self.languages[0])
            if lang_name == "Arabic":
                template = messages_config.get('language_instructions.single_language_arabic',
                                               "**Language Instructions:**\n- You should respond **only** in {language_name}. Use UAE accent.")
            else:
                template = messages_config.get('language_instructions.single_language',
                                               "**Language Instructions:**\n- You should respond **only** in {language_name}.")
            return template.format(language_name=lang_name)

        supported_langs = [self.map_language_code(lang)[0] for lang in self.languages]
        default_lang, _ = self.map_language_code(self.languages[0])
        template = messages_config.get('language_instructions.multiple_languages',
                                       "**Language Instructions:**\n- You should respond in the same language as the user, supporting {supported_languages}.\n- Default language is {default_language}.")
        return template.format(supported_languages=', '.join(supported_langs), default_language=default_lang)

    def get_locale(self, lang_code: str = None) -> str:
        if not lang_code:
            return self.default_language
        _, locale = self.map_language_code(lang_code)
        return locale
