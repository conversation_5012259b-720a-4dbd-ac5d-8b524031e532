from config import load_config
from .turn_feature_detector import TurnFeatureDetector


class ContextualAnswerDetector(TurnFeatureDetector):
	def __init__(self, ack_set=None):
        if ack_set is None:
            messages_config = load_config('messages')
            self.ack_set = set(messages_config.get('acknowledgment_phrases', []))
        else:
            self.ack_set = ack_set
	
	def check(self, chat_ctx):
		msgs = chat_ctx.items
		last_agent = next((m.text_content.strip() for m in reversed(msgs) if m.role == "assistant"), "")
		last_user = next((m.text_content.lower().strip() for m in reversed(msgs) if m.role == "user"), "")
		if last_agent.strip().endswith("?") and last_user in self.ack_set:
			return True
		return False
